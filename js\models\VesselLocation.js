// const { listThings } = require("../modules/awsIot")
const mongoose = require('mongoose');
const db = require("../modules/db");

let VesselLocation = {}

// listThings().then(regions => {

//     regions.forEach(data => {
//         const region = data.region
//         const things = data.things.filter(thing => thing.thingTypeName === 'smartmast')

//         if (things.length === 0) return console.info(`No applicable things in region ${region}`)

//         things.forEach(thing => {
//             const vesselName = thing.thingName
//             const collection = `${vesselName}_location`

//             const VesselLocationSchema = new mongoose.Schema({
//                 latitude: { type: Number, required: true },
//                 longitude: { type: Number, required: true },
//                 groundSpeed: { type: Number, required: true },
//                 timestamp: { type: Date, required: false, default: () => new Date().toISOString(), index: true }
//             }, { minimize: false });

//             VesselLocation[collection] = db.qmLocations.model(collection, VesselLocationSchema, collection);
//         })

//     })
// }).catch(err => {
//     console.error(`things list error ${err}`)
// })

const getLocationCollection = async (vesselName) => {
    const collection = `${vesselName}_location`

    if (VesselLocation[collection]) return VesselLocation[collection]

    // Check if collection exists and if it's a timeseries collection
    const collections = await db.qmLocations.db.listCollections({ name: collection }).toArray();
    const collectionExists = collections.length > 0;
    const isTimeseries = collectionExists && collections[0].type === 'timeseries';

    // If collection exists but is not a timeseries collection, we need to recreate it
    if (collectionExists && !isTimeseries) {
        console.log(`[VesselLocation] Collection ${collection} exists but is not a timeseries collection. Recreating...`);
        await db.qmLocations.db.dropCollection(collection);
    }

    const VesselLocationSchema = new mongoose.Schema({
        latitude: { type: Number, required: true },
        longitude: { type: Number, required: true },
        groundSpeed: { type: Number, required: true },
        isStationary: { type: Boolean, required: true, default: false },
        headingMotion: { type: Number, required: false },
        accuracyHeading: { type: Number, required: false },
        onboardVesselId: { type: mongoose.Schema.Types.ObjectId, required: false },
        metadata: { type: Object, required: false },
        timestamp: { type: Date, required: false, default: () => new Date().toISOString(), index: true }
    }, { minimize: false, timeseries: { timeField: 'timestamp', metaField: 'metadata', granularity: 'seconds' } });

    // If collection doesn't exist or was dropped, create it explicitly as timeseries
    if (!collectionExists || !isTimeseries) {
        try {
            await db.qmLocations.db.createCollection(collection, {
                timeseries: {
                    timeField: 'timestamp',
                    metaField: 'metadata',
                    granularity: 'seconds'
                }
            });
            console.log(`[VesselLocation] Created timeseries collection: ${collection}`);
        } catch (error) {
            // Collection might already exist, ignore the error
            if (!error.message.includes('already exists')) {
                console.error(`[VesselLocation] Error creating timeseries collection ${collection}:`, error);
            }
        }
    }

    VesselLocation[collection] = db.qmLocations.model(collection, VesselLocationSchema, collection);

    // Create indexes after model creation
    try {
        await VesselLocation[collection].collection.createIndex({ onboardVesselId: 1, timestamp: 1 });
    } catch (error) {
        // Index might already exist, ignore the error
        if (!error.message.includes('already exists')) {
            console.error(`[VesselLocation] Error creating index for ${collection}:`, error);
        }
    }

    return VesselLocation[collection]
}

module.exports = {
    getLocationCollection
};


// db.getCollectionNames().forEach(function(c) {
//     if (c.endsWith("_location")) {
//       db[c].drop();
//     }
//   });