const { getLocationCollection } = require('../models/VesselLocation');
const { createLoggerWithPath } = require('../modules/winston');
const { calculateDistanceInMeters, getVesselIdbyUnitId } = require('../utils/functions');
const io = require('../modules/io');
const thingsboardService = require('./thingsboard.service');
const { getLocationRawCollection } = require('../models/LocationRaw');
const { default: mongoose } = require('mongoose');

const locationUpdateInterval = 60000 // in milliseconds

async function checkIsStationaryCoordinate(collection, latitude, longitude) {
    const WINDOW_SIZE = 4;
    const DISTANCE_THRESHOLD = 50;
    const STATIONARY_CHECK_MINUTES = 5;

    const minutesAgo = new Date(Date.now() - (STATIONARY_CHECK_MINUTES * 60 * 1000));
    const lastPoints = await collection
        .find({ timestamp: { $gt: minutesAgo } })
        .limit(WINDOW_SIZE);

    const distances = await Promise.all(lastPoints.map(point =>
        calculateDistanceInMeters(
            latitude,
            longitude,
            point.latitude,
            point.longitude
        )
    ));

    const isStationary = distances.every(distance => {
        return distance <= DISTANCE_THRESHOLD;
    });

    return isStationary;
}

const lastReadings = {}

async function processIotGpsMessage(topic, message, region) {
    if (!topic.endsWith('/gps/status')) return;
    // console.log('[GPS Service] processing message from topic', topic, 'in region', region)
    const unit_id = topic.split('/').shift();

    const logger = createLoggerWithPath(`mqtt/${region}/${unit_id}`);

    try {
        const decoder = new TextDecoder('utf-8');
        const messageString = decoder.decode(message);
        const data = JSON.parse(messageString);

        if (data.valid_gnss_fix) {
            const { latitude, longitude, ground_speed: groundSpeed, heading_motion: headingMotion, accuracy_heading: accuracyHeading } = data;

            const timestampMs = Number(data.header.stamp.sec) * 1000
            const timestampISO = new Date(timestampMs).toISOString()
            const isoSplit = timestampISO.split("-");
            const yearMonth = isoSplit[0] + "-" + isoSplit[1];

            // TODO: update socket
            io.emit(`${unit_id}/gps`, {
                vesselName: unit_id,
                latitude,
                longitude,
                groundSpeed,
                headingMotion,
                accuracyHeading,
                timestamp: timestampMs,
                metadata: data
            });

            if (process.env.NODE_ENV === 'prod') {
                await thingsboardService.processSensorData({
                    deviceName: unit_id,
                    telemetry: {
                        gps_latitude: latitude,
                        gps_longitude: longitude,
                        gps_ground_speed: groundSpeed,
                        gps_heading_motion: headingMotion,
                        gps_accuracy_heading: accuracyHeading,
                        gps_timestamp: timestampISO
                    }
                })
            }

            const lastReading = lastReadings[unit_id];
            const lastProcessedWithinInterval = lastReading && lastReading.getTime() + locationUpdateInterval > new Date().getTime();

            if (lastProcessedWithinInterval) return;

            lastReadings[unit_id] = new Date();

            const collection = await getLocationCollection(unit_id);
            if (!collection) throw new Error(`[GPS Service] Unable to find collection for ${unit_id}`);

            const rawCollection = getLocationRawCollection(yearMonth);
            if (!rawCollection) throw new Error(`[GPS Service] Unable to find raw collection for ${unit_id}`);

            /** optimized to use local variable instead of querying db every invocation */
            // const lastRecord = await collection.findOne({
            //     timestamp: { $gt: new Date().getTime() - locationUpdateInterval },
            // });
            // if (lastRecord) return;

            const isStationary = await checkIsStationaryCoordinate(collection, latitude, longitude);

            const onboardVesselId = await getVesselIdbyUnitId(unit_id);

            logger.info(`[GPS Service] Received GPS Coordinates for unit ${unit_id} in Region ${region}: Latitude = ${latitude}, Longitude = ${longitude}, Ground Speed = ${groundSpeed}, Is Stationary = ${isStationary}, Heading Motion = ${headingMotion}, Accuracy Heading = ${accuracyHeading}`);

            // TODO: remove legacy implementation once new implementation is stable in production
            const legacyLocationData = {
                latitude,
                longitude,
                groundSpeed,
                isStationary,
                headingMotion,
                accuracyHeading,
                onboardVesselId,
                metadata: data,
                timestamp: timestampISO
            };

            const dbRes = await collection.create(legacyLocationData)

            // TODO: update socket
            io.emit(`${unit_id}_location/insert`, { unit_id, ...dbRes.toObject() })

            const rawLocationData = {
                location: {
                    type: "Point",
                    coordinates: [longitude, latitude]
                },
                groundSpeed,
                isStationary,
                headingMotion,
                accuracyHeading,
                metadata: {
                    onboardVesselId,
                    unitId: unit_id
                },
                details: data,
                timestamp: timestampISO
            };

            await rawCollection.create(rawLocationData)

        } else {
            logger.error(`[GPS Service] Received Invalid GPS Coordinates for Vessel ${unit_id} in Region ${region}: Latitude = ${data.latitude}, Longitude = ${data.longitude}`);
        }
    } catch (error) {
        logger.error(`[GPS Service] Error processing MQTT message for Vessel ${unit_id} in Region ${region}`, JSON.stringify(error));
        throw error;
    }
}

module.exports = {
    processIotGpsMessage
}
