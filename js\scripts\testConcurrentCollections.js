require('dotenv').config();
const db = require('../modules/db');
const { getLocationCollection } = require('../models/VesselLocation');

async function testConcurrentCollections() {
    console.log('🧪 Testing concurrent getLocationCollection calls...\n');

    const testUnitId = 'test-concurrent-unit';
    const collectionName = `${testUnitId}_location`;

    try {
        // Clean up any existing test collection
        try {
            await db.qmLocations.db.dropCollection(collectionName);
            console.log(`🗑️  Dropped existing collection: ${collectionName}`);
        } catch (error) {
            console.log(`ℹ️  No existing collection to drop: ${collectionName}`);
        }

        console.log(`\n📞 Calling getLocationCollection 10 times simultaneously for: ${testUnitId}`);
        
        // Record start time
        const startTime = Date.now();

        // Create 10 concurrent calls
        const promises = Array.from({ length: 10 }, (_, index) => {
            return new Promise(async (resolve) => {
                const callStart = Date.now();
                console.log(`🚀 Call ${index + 1} started at ${callStart - startTime}ms`);
                
                try {
                    const collection = getLocationCollection(testUnitId);
                    const callEnd = Date.now();
                    
                    resolve({
                        callIndex: index + 1,
                        collection: collection,
                        collectionName: collection.collection.name,
                        modelName: collection.modelName,
                        duration: callEnd - callStart,
                        timestamp: callEnd - startTime,
                        success: true
                    });
                } catch (error) {
                    const callEnd = Date.now();
                    resolve({
                        callIndex: index + 1,
                        error: error.message,
                        duration: callEnd - callStart,
                        timestamp: callEnd - startTime,
                        success: false
                    });
                }
            });
        });

        // Wait for all calls to complete
        const results = await Promise.all(promises);
        const totalTime = Date.now() - startTime;

        console.log(`\n📊 Results after ${totalTime}ms:`);
        console.log('='.repeat(80));

        // Analyze results
        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);

        console.log(`✅ Successful calls: ${successful.length}`);
        console.log(`❌ Failed calls: ${failed.length}`);

        if (successful.length > 0) {
            console.log(`\n📈 Successful call details:`);
            successful.forEach(result => {
                console.log(`   Call ${result.callIndex}: ${result.duration}ms (at ${result.timestamp}ms) - Collection: ${result.collectionName}`);
            });

            // Check if all successful calls returned the same collection instance
            const firstCollection = successful[0].collection;
            const allSameInstance = successful.every(result => result.collection === firstCollection);
            console.log(`\n🔗 All calls returned same collection instance: ${allSameInstance ? '✅ YES' : '❌ NO'}`);

            // Check collection names
            const uniqueCollectionNames = [...new Set(successful.map(r => r.collectionName))];
            console.log(`📝 Unique collection names: ${uniqueCollectionNames.length} (${uniqueCollectionNames.join(', ')})`);
        }

        if (failed.length > 0) {
            console.log(`\n❌ Failed call details:`);
            failed.forEach(result => {
                console.log(`   Call ${result.callIndex}: ${result.error} (${result.duration}ms at ${result.timestamp}ms)`);
            });
        }

        // Test the collection by checking if it exists in MongoDB
        console.log(`\n🔍 Checking MongoDB collection status:`);
        const collections = await db.qmLocations.db.listCollections({ name: collectionName }).toArray();
        
        if (collections.length > 0) {
            const collectionInfo = collections[0];
            console.log(`   ✅ Collection exists: ${collectionInfo.name}`);
            console.log(`   📊 Collection type: ${collectionInfo.type || 'regular'}`);
            console.log(`   ⚙️  Collection options:`, JSON.stringify(collectionInfo.options || {}, null, 2));
        } else {
            console.log(`   ❌ Collection does not exist in MongoDB yet`);
        }

        // Test data insertion with one of the successful collections
        if (successful.length > 0) {
            console.log(`\n💾 Testing data insertion with first successful collection:`);
            const testCollection = successful[0].collection;
            
            const testData = {
                latitude: 40.7128,
                longitude: -74.0060,
                groundSpeed: 10.5,
                isStationary: false,
                headingMotion: 45,
                accuracyHeading: 2.5,
                metadata: { test: true, concurrent: true },
                timestamp: new Date().toISOString()
            };

            try {
                const insertResult = await testCollection.create(testData);
                console.log(`   ✅ Data inserted successfully: ${insertResult._id}`);
                
                // Verify the collection type after insertion
                const collectionsAfterInsert = await db.qmLocations.db.listCollections({ name: collectionName }).toArray();
                if (collectionsAfterInsert.length > 0) {
                    console.log(`   📊 Collection type after insert: ${collectionsAfterInsert[0].type || 'regular'}`);
                }
            } catch (error) {
                console.log(`   ❌ Data insertion failed: ${error.message}`);
            }
        }

        console.log(`\n🎯 Summary:`);
        console.log(`   Total execution time: ${totalTime}ms`);
        console.log(`   Success rate: ${(successful.length / results.length * 100).toFixed(1)}%`);
        console.log(`   Average call duration: ${(successful.reduce((sum, r) => sum + r.duration, 0) / successful.length).toFixed(1)}ms`);

    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        // Clean up
        try {
            await db.qmLocations.db.dropCollection(collectionName);
            console.log(`\n🧹 Cleaned up test collection: ${collectionName}`);
        } catch (error) {
            // Ignore cleanup errors
        }
        
        console.log('\n✨ Test completed!');
        process.exit(0);
    }
}

testConcurrentCollections();
