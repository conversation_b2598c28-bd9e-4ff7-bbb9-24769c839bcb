require('dotenv').config();
const db = require('../modules/db');
const { getLocationCollection } = require('../models/VesselLocation');

function testConcurrentCollections() {
    const testUnitId = 'test-concurrent-unit';

    // Call getLocationCollection 10 times
    for (let i = 1; i <= 10; i++) {
        try {
            const collection = getLocationCollection(testUnitId);
            console.log(`Call ${i}: success`);
        } catch (error) {
            console.log(`Call ${i}: error - ${error.message}`);
        }
    }

    process.exit(0);
}

db.qmLocations.on('open', () => {
    console.log('DB connected to Locations');
    testConcurrentCollections();
});
