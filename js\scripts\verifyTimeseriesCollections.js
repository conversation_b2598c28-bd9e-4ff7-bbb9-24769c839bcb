require('dotenv').config();
const db = require('../modules/db');
const { getLocationCollection } = require('../models/VesselLocation');
const { getLocationRawCollection } = require('../models/LocationRaw');
const { getAisCollection } = require('../models/VesselAis');

async function verifyTimeseriesCollections() {
    console.log('🔍 Verifying timeseries collections...\n');

    try {
        // Test VesselLocation collection
        console.log('Testing VesselLocation collection...');
        const testUnitId = 'test-unit';
        const locationCollection = await getLocationCollection(testUnitId);
        
        // Check if collection is timeseries
        const locationCollections = await db.qmLocations.db.listCollections({ name: `${testUnitId}_location` }).toArray();
        const isLocationTimeseries = locationCollections.length > 0 && locationCollections[0].type === 'timeseries';
        console.log(`✅ ${testUnitId}_location collection type: ${isLocationTimeseries ? 'timeseries' : 'regular'}`);

        // Test LocationRaw collection
        console.log('\nTesting LocationRaw collection...');
        const testMonth = '2024-01';
        const rawCollection = await getLocationRawCollection(testMonth);
        
        // Check if collection is timeseries
        const rawCollections = await db.locationsRaw.db.listCollections({ name: testMonth }).toArray();
        const isRawTimeseries = rawCollections.length > 0 && rawCollections[0].type === 'timeseries';
        console.log(`✅ ${testMonth} collection type: ${isRawTimeseries ? 'timeseries' : 'regular'}`);

        // Test VesselAis collection
        console.log('\nTesting VesselAis collection...');
        const aisCollection = await getAisCollection(testUnitId);
        
        // Check if collection is timeseries
        const aisCollections = await db.qmAis.db.listCollections({ name: `${testUnitId}_ais` }).toArray();
        const isAisTimeseries = aisCollections.length > 0 && aisCollections[0].type === 'timeseries';
        console.log(`✅ ${testUnitId}_ais collection type: ${isAisTimeseries ? 'timeseries' : 'regular'}`);

        // Test data insertion
        console.log('\n📝 Testing data insertion...');
        
        const testLocationData = {
            latitude: 40.7128,
            longitude: -74.0060,
            groundSpeed: 10.5,
            isStationary: false,
            headingMotion: 45,
            accuracyHeading: 2.5,
            metadata: { test: true },
            timestamp: new Date().toISOString()
        };

        const locationResult = await locationCollection.create(testLocationData);
        console.log(`✅ Location data inserted with ID: ${locationResult._id}`);

        const testRawData = {
            location: {
                type: "Point",
                coordinates: [-74.0060, 40.7128]
            },
            groundSpeed: 10.5,
            isStationary: false,
            headingMotion: 45,
            accuracyHeading: 2.5,
            metadata: { test: true, unitId: testUnitId },
            details: { test: true },
            timestamp: new Date().toISOString()
        };

        const rawResult = await rawCollection.create(testRawData);
        console.log(`✅ Raw location data inserted with ID: ${rawResult._id}`);

        const testAisData = {
            location: {
                type: "Point",
                coordinates: [-74.0060, 40.7128]
            },
            mmsi: "123456789",
            name: "Test Vessel",
            metadata: { test: true },
            timestamp: new Date(),
            creation_timestamp: new Date()
        };

        const aisResult = await aisCollection.create(testAisData);
        console.log(`✅ AIS data inserted with ID: ${aisResult._id}`);

        // Test data retrieval
        console.log('\n📖 Testing data retrieval...');
        
        const locationCount = await locationCollection.countDocuments();
        const rawCount = await rawCollection.countDocuments();
        const aisCount = await aisCollection.countDocuments();
        
        console.log(`✅ Location collection has ${locationCount} documents`);
        console.log(`✅ Raw collection has ${rawCount} documents`);
        console.log(`✅ AIS collection has ${aisCount} documents`);

        console.log('\n🎉 All tests passed! Timeseries collections are working correctly.');

    } catch (error) {
        console.error('❌ Error during verification:', error);
    } finally {
        // Close database connections
        await db.qmLocations.close();
        await db.locationsRaw.close();
        await db.qmAis.close();
        process.exit(0);
    }
}

// Run the verification
verifyTimeseriesCollections();
